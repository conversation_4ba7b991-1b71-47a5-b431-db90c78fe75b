defmodule PetalProWeb.NotificationsLiveTest do
  @moduledoc """
  Tests to understand Phoenix LiveView's behavior with unhandled messages.

  Key findings:
  1. Phoenix LiveView provides a default handle_info/2 implementation that gracefully
     ignores unmatched messages when no explicit handle_info/2 is defined.
  2. When a LiveView explicitly implements handle_info/2 with specific clauses,
     unmatched messages cause FunctionClauseError crashes.
  3. DashboardLive subscribes to user notifications through on_mount hooks but only
     handles :refresh messages, causing crashes on notification broadcasts.
  4. The notification system is designed to be unobtrusive through client-side
     JavaScript channels rather than server-side LiveView handle_info/2.
  """
  use PetalProWeb.ConnCase

  import PetalPro.AccountsFixtures
  import Phoenix.LiveViewTest

  describe "LiveView stability with unhandled notifications" do
    setup :register_and_sign_in_user

    test "LiveView handles unmatched Phoenix.Socket.Broadcast gracefully" do
      # Define a minimal test-only LiveView module within the test
      defmodule TestLiveView do
        use Phoenix.LiveView

        @impl true
        def mount(_params, _session, socket) do
          # Subscribe to user notifications topic to receive broadcasts
          user_id = socket.assigns[:user_id] || 123
          Phoenix.PubSub.subscribe(PetalPro.PubSub, PetalPro.Notifications.user_notifications_topic(user_id))

          {:ok, Phoenix.Component.assign(socket, :test_state, "initial")}
        end

        @impl true
        def render(assigns) do
          ~H"""
          <div id="test-liveview">
            <p>Test State: {@test_state}</p>
          </div>
          """
        end

        @impl true
        def handle_info(:test_event, socket) do
          {:noreply, Phoenix.Component.assign(socket, :test_state, "test_event_handled")}
        end

        # Intentionally NOT implementing handle_info for Phoenix.Socket.Broadcast messages
        # This should demonstrate the unobtrusive behavior - the LiveView should not crash
        # when it receives notification broadcasts it doesn't explicitly handle
      end

      # Set up user for the notification
      user = user_fixture()

      # Start the test LiveView with user_id in assigns
      {:ok, view, _html} = live_isolated(build_conn(), TestLiveView, session: %{"user_id" => user.id})

      # Verify initial state
      assert render(view) =~ "Test State: initial"

      # Get the LiveView process PID
      lv_pid = view.pid

      # Verify the process is alive before sending the notification
      assert Process.alive?(lv_pid)

      # Simulate a notification broadcast using the actual notification system
      # This is what happens when Notifications.broadcast_user_notification/1 is called
      PetalProWeb.Endpoint.broadcast(
        PetalPro.Notifications.user_notifications_topic(user.id),
        "notifications_updated",
        %{id: 123, type: :invited_to_org}
      )

      # Give the process a moment to handle (or not handle) the broadcast
      Process.sleep(50)

      # Assert that the LiveView process remains alive and doesn't crash
      assert Process.alive?(lv_pid)

      # Verify the LiveView still responds correctly to events it handles
      send(lv_pid, :test_event)
      Process.sleep(10)
      assert render(view) =~ "Test State: test_event_handled"
    end

    test "DashboardLive fails with expected FunctionClauseError when receiving unhandled broadcasts" do
      # Set up confirmed user for the notification
      user = confirmed_user_fixture(%{is_onboarded: true})

      # Start the actual DashboardLive with proper authentication context
      # This triggers the on_mount hooks that subscribe to user notifications
      conn = log_in_user(build_conn(), user)
      {:ok, view, _html} = live(conn, "/app")

      # Get the LiveView process PID
      lv_pid = view.pid

      # Verify the process is alive before sending the notification
      assert Process.alive?(lv_pid)

      # Monitor the process to catch the expected crash
      Process.monitor(lv_pid)

      # Simulate a notification broadcast that DashboardLive doesn't explicitly handle
      # DashboardLive only has handle_info(:refresh, socket) so this causes
      # a FunctionClauseError when the LiveView receives the Phoenix.Socket.Broadcast
      PetalProWeb.Endpoint.broadcast(
        PetalPro.Notifications.user_notifications_topic(user.id),
        "notifications_updated",
        %{id: 123, type: :invited_to_org}
      )

      # Wait for the expected crash with the specific error
      assert_receive {:DOWN, _ref, :process, ^lv_pid,
                      {:function_clause,
                       [
                         {PetalProWeb.DashboardLive, :handle_info,
                          [%Phoenix.Socket.Broadcast{event: "notifications_updated"}, _socket], _}
                         | _
                       ]}},
                     1000

      # Verify the process has crashed
      refute Process.alive?(lv_pid)
    end

    test "LiveView with explicit handle_info clauses would fail with FunctionClauseError" do
      # Define a LiveView that explicitly defines handle_info with specific patterns
      # and doesn't have a catch-all clause
      defmodule StrictTestLiveView do
        use Phoenix.LiveView

        @impl true
        def mount(_params, _session, socket) do
          # Subscribe to user notifications topic to receive broadcasts
          user_id = socket.assigns[:user_id] || 123
          Phoenix.PubSub.subscribe(PetalPro.PubSub, PetalPro.Notifications.user_notifications_topic(user_id))

          {:ok, Phoenix.Component.assign(socket, :test_state, "initial")}
        end

        @impl true
        def render(assigns) do
          ~H"""
          <div id="test-liveview">
            <p>Test State: {@test_state}</p>
          </div>
          """
        end

        # Only handle specific messages - no catch-all clause
        @impl true
        def handle_info(:specific_message, socket) do
          {:noreply, Phoenix.Component.assign(socket, :test_state, "specific_handled")}
        end

        # This would cause a FunctionClauseError for any other message type
        # because there's no catch-all clause and no default implementation is used
        # when you explicitly implement handle_info/2
      end

      # Set up user for the notification
      user = user_fixture()

      # Start the strict test LiveView
      {:ok, view, _html} = live_isolated(build_conn(), StrictTestLiveView, session: %{"user_id" => user.id})

      # Get the LiveView process PID
      lv_pid = view.pid

      # Verify the process is alive before sending the notification
      assert Process.alive?(lv_pid)

      # Simulate a notification broadcast that StrictTestLiveView doesn't handle
      # This should cause a FunctionClauseError because there's no matching clause
      PetalProWeb.Endpoint.broadcast(
        PetalPro.Notifications.user_notifications_topic(user.id),
        "notifications_updated",
        %{id: 123, type: :invited_to_org}
      )

      # Give the process a moment to handle the broadcast and crash
      Process.sleep(50)

      # The process should have crashed due to FunctionClauseError
      refute Process.alive?(lv_pid)
    end

    test "Custom LiveView with explicit handle_info crashes with FunctionClauseError on unhandled broadcasts" do
      # Define a custom LiveView inline that subscribes to notifications and has explicit handle_info clauses
      defmodule CustomCrashingLiveView do
        use Phoenix.LiveView

        @impl true
        def mount(_params, _session, socket) do
          # Subscribe to user notifications topic to receive broadcasts
          user_id = socket.assigns[:user_id] || 123
          Phoenix.PubSub.subscribe(PetalPro.PubSub, PetalPro.Notifications.user_notifications_topic(user_id))

          {:ok, Phoenix.Component.assign(socket, :test_state, "initial")}
        end

        @impl true
        def render(assigns) do
          ~H"""
          <div id="custom-liveview">
            <p>Test State: {@test_state}</p>
          </div>
          """
        end

        # Explicit handle_info clause for a specific message
        @impl true
        def handle_info(:some_message, socket) do
          {:noreply, Phoenix.Component.assign(socket, :test_state, "some_message_handled")}
        end

        # Intentionally NOT implementing handle_info for Phoenix.Socket.Broadcast messages
        # This will cause a FunctionClauseError when notification broadcasts are received
      end

      # Set up user for the notification
      user = user_fixture()

      # Start the custom LiveView with user_id in assigns
      {:ok, view, _html} = live_isolated(build_conn(), CustomCrashingLiveView, session: %{"user_id" => user.id})

      # Verify initial state
      assert render(view) =~ "Test State: initial"

      # Get the LiveView process PID
      lv_pid = view.pid

      # Verify the process is alive before sending the notification
      assert Process.alive?(lv_pid)

      # Monitor the process to catch the expected crash
      Process.monitor(lv_pid)

      # Verify the LiveView can handle its explicit message type
      send(lv_pid, :some_message)
      Process.sleep(10)
      assert Process.alive?(lv_pid)
      assert render(view) =~ "Test State: some_message_handled"

      # Now simulate a notification broadcast that the LiveView doesn't explicitly handle
      # This should cause a FunctionClauseError because there's no matching handle_info clause
      # for Phoenix.Socket.Broadcast messages
      PetalProWeb.Endpoint.broadcast(
        PetalPro.Notifications.user_notifications_topic(user.id),
        "notifications_updated",
        %{id: 456, type: :invited_to_org}
      )

      # Wait for the expected crash with the specific FunctionClauseError
      assert_receive {:DOWN, _ref, :process, ^lv_pid,
                      {:function_clause,
                       [
                         {CustomCrashingLiveView, :handle_info,
                          [%Phoenix.Socket.Broadcast{event: "notifications_updated"}, _socket], _}
                         | _
                       ]}},
                     1000

      # Verify the process has crashed
      refute Process.alive?(lv_pid)
    end

    test "Understanding Phoenix LiveView's default handle_info behavior" do
      # Define a LiveView that explicitly defines handle_info with specific patterns
      defmodule DebugTestLiveView do
        use Phoenix.LiveView

        @impl true
        def mount(_params, _session, socket) do
          {:ok, Phoenix.Component.assign(socket, :test_state, "initial")}
        end

        @impl true
        def render(assigns) do
          ~H"""
          <div id="test-liveview">
            <p>Test State: {@test_state}</p>
          </div>
          """
        end

        # Only handle specific messages - no catch-all clause
        @impl true
        def handle_info(:specific_message, socket) do
          {:noreply, Phoenix.Component.assign(socket, :test_state, "specific_handled")}
        end
      end

      # Set up user for the notification
      user = user_fixture()

      # Start the debug test LiveView
      {:ok, view, _html} = live_isolated(build_conn(), DebugTestLiveView, session: %{"user_id" => user.id})

      # Get the LiveView process PID
      lv_pid = view.pid

      # Verify the process is alive before sending messages
      assert Process.alive?(lv_pid)

      # Test 1: Send a message that matches the handle_info clause
      send(lv_pid, :specific_message)
      Process.sleep(10)
      assert Process.alive?(lv_pid)
      assert render(view) =~ "Test State: specific_handled"

      # Test 2: Send a random message that doesn't match any clause
      send(lv_pid, :random_unhandled_message)
      Process.sleep(10)
      # If Phoenix LiveView has a default handle_info, this should not crash
      assert Process.alive?(lv_pid)

      # Test 3: Send a tuple message
      send(lv_pid, {:some, :tuple, :message})
      Process.sleep(10)
      assert Process.alive?(lv_pid)

      # Test 4: Send a Phoenix.Socket.Broadcast message directly
      broadcast_msg = %Phoenix.Socket.Broadcast{
        topic: "test_topic",
        event: "test_event",
        payload: %{test: "data"}
      }

      send(lv_pid, broadcast_msg)
      Process.sleep(10)
      assert Process.alive?(lv_pid)
    end
  end
end
